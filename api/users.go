package api

import (
	"net/http"
	"time"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var db *gorm.DB

type User struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Name      string    `gorm:"size:255;not null" json:"name"`
	Email     string    `gorm:"size:255;uniqueIndex;not null" json:"email"`
	Password  string    `gorm:"size:255;not null" json:"-"`
	Role      string    `gorm:"size:50;not null" json:"role"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func initDB() {
	dsn := "youruser:yourpass@tcp(localhost:3306)/yourdb?charset=utf8mb4&parseTime=True&loc=Local"
	var err error
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Auto migrate models
	db.AutoMigrate(&User{}, &Backup{}, &Integration{})
}

func createUser(c *gin.Context) {
	var newUser User

	if err := c.ShouldBindJSON(&newUser); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	// Validação de email único
	var existingUser User
	if err := db.Where("email = ?", newUser.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Email já está em uso"})
		return
	}

	// Hash da senha
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newUser.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao gerar hash da senha"})
		return
	}
	newUser.Password = string(hashedPassword)

	// Criação do usuário
	if err := db.Create(&newUser).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar usuário"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Usuário criado com sucesso",
		"user":    newUser,
	})
}

func setupRoutes(router *gin.Engine) {
	// Rotas públicas
	router.POST("/api/login", login)
	router.POST("/api/password/reset", requestPasswordReset)
	router.POST("/api/password/change", resetPassword)
	router.POST("/api/refresh", refreshToken)

	// Rotas protegidas
	userGroup := router.Group("/api/users")
	userGroup.Use(middleware.AuthMiddleware())
	{
		userGroup.POST("", roleMiddleware("admin"), createUser)
		userGroup.GET("", roleMiddleware("admin"), listUsers)
		userGroup.GET("/:id", getUser)
		userGroup.PUT("/:id", updateUser)
		userGroup.DELETE("/:id", roleMiddleware("admin"), deleteUser)
	}

	// Novas rotas de configurações
	configGroup := router.Group("/api/config")
	configGroup.Use(middleware.AuthMiddleware())
	{
		configGroup.GET("/backups", roleMiddleware("admin"), listBackups)
		configGroup.POST("/backups", roleMiddleware("admin"), createBackup)
		configGroup.POST("/restore", roleMiddleware("admin"), restoreBackup)
		configGroup.GET("/logs", roleMiddleware("admin"), getLogs)
		configGroup.POST("/integrations", roleMiddleware("admin"), createIntegration)
	}
}

func getUser(c *gin.Context) {
	id := c.Param("id")
	var user User

	if err := db.First(&user, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

func updateUser(c *gin.Context) {
	id := c.Param("id")
	var user User

	if err := db.First(&user, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	var updateData struct {
		Name  string `json:"name"`
		Email string `json:"email"`
		Role  string `json:"role"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Update fields
	user.Name = updateData.Name
	user.Email = updateData.Email
	user.Role = updateData.Role

	if err := db.Save(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User updated successfully",
		"user":    user,
	})
}

func deleteUser(c *gin.Context) {
	id := c.Param("id")

	// Check if user exists
	var user User
	if err := db.First(&user, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	if err := db.Delete(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

func listUsers(c *gin.Context) {
	var users []User
	db.Find(&users)
	c.JSON(http.StatusOK, users)
}
