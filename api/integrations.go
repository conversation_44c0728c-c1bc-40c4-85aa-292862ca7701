package api

import (
	"net/http"
	"time"
	"tradicao/internal/middleware"

	"github.com/gin-gonic/gin"
)

func createIntegrationHandler(c *gin.Context) {
	var integration Integration

	if err := c.ShouldBindJSON(&integration); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Basic validation
	if integration.Name == "" || integration.Type == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Name and type are required"})
		return
	}

	// Set creation time
	integration.CreatedAt = time.Now()

	if err := db.Create(&integration).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create integration"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Integration created successfully",
		"integration": integration,
	})
}

func setupIntegrationRoutes(router *gin.Engine) {
	integrationGroup := router.Group("/api/integrations")
	integrationGroup.Use(middleware.AuthMiddleware())
	{
		integrationGroup.POST("", roleMiddleware("admin"), createIntegrationHandler)
	}
}
