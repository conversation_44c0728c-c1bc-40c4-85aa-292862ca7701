package middleware

import (
	"log"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// OrderPermissionMiddleware verifica se o usuário tem permissão para acessar a ordem
// Esta função é um redirecionamento para o ResourcePermissionMiddleware
func OrderPermissionMiddleware() gin.HandlerFunc {
	log.Println("AVISO: OrderPermissionMiddleware está obsoleto. Use ResourcePermissionMiddleware em vez disso.")

	// Usar o middleware de permissão de recurso com o tipo de recurso "order" e a ação "view"
	return ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionView)
}
