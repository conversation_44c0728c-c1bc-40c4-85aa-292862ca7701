package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ProviderMiddleware verifica se o usuário é um prestador
func ProviderMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verificar se o usuário está autenticado
		userID, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Usuário não autenticado"})
			c.Abort()
			return
		}

		// Verificar se o usuário tem o perfil de prestador
		userRole, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado. Perfil de usuário não encontrado."})
			c.Abort()
			return
		}

		// Verificar se é um prestador (provider, prestadores, prestador)
		userRoleStr := userRole.(string)
		if userRoleStr != "provider" && userRoleStr != "prestadores" && userRoleStr != "prestador" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado. Apenas prestadores podem acessar este recurso."})
			c.Abort()
			return
		}

		// Obter o ID do prestador associado ao usuário
		// Em um sistema real, isso seria obtido do banco de dados
		// Aqui, apenas simulamos com o mesmo ID do usuário
		providerID := userID

		// Adicionar o ID do prestador ao contexto
		c.Set("providerID", providerID)

		c.Next()
	}
}
