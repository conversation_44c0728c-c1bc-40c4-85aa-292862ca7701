package middleware

import (
	"log"
	"sync"
	"time"
)

// TokenBlacklist gerencia tokens JWT invalidados
type TokenBlacklist struct {
	blacklist map[string]time.Time // Mapa de tokens para tempo de expiração
	mutex     sync.RWMutex         // Mutex para acesso concorrente seguro
}

// NewTokenBlacklist cria uma nova instância de TokenBlacklist
func NewTokenBlacklist() *TokenBlacklist {
	bl := &TokenBlacklist{
		blacklist: make(map[string]time.Time),
	}

	// Iniciar rotina de limpeza de tokens expirados
	go bl.cleanupRoutine()

	return bl
}

// AddToken adiciona um token à blacklist com um tempo de expiração
func (bl *TokenBlacklist) AddToken(token string, expiry time.Time) {
	bl.mutex.Lock()
	defer bl.mutex.Unlock()

	bl.blacklist[token] = expiry
	log.Printf("[BLACKLIST] Token adicionado à blacklist. Expira em: %v", expiry)
}

// IsBlacklisted verifica se um token está na blacklist
func (bl *TokenBlacklist) IsBlacklisted(token string) bool {
	bl.mutex.RLock()
	defer bl.mutex.RUnlock()

	_, exists := bl.blacklist[token]
	return exists
}

// cleanupRoutine remove periodicamente tokens expirados da blacklist
func (bl *TokenBlacklist) cleanupRoutine() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		bl.cleanup()
	}
}

// cleanup remove tokens expirados da blacklist
func (bl *TokenBlacklist) cleanup() {
	bl.mutex.Lock()
	defer bl.mutex.Unlock()

	now := time.Now()
	count := 0
	for token, expiry := range bl.blacklist {
		if now.After(expiry) {
			delete(bl.blacklist, token)
			count++
		}
	}

	if count > 0 {
		log.Printf("[BLACKLIST] Limpeza concluída. %d tokens expirados removidos.", count)
	}
}
