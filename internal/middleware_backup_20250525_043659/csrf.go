package middleware

import (
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	// CSRFTokenLength é o comprimento do token CSRF em bytes
	CSRFTokenLength = 32
	// CSRFTokenCookieName é o nome do cookie que armazena o token CSRF
	CSRFTokenCookieName = "csrf_token"
	// CSRFTokenHeaderName é o nome do cabeçalho HTTP para o token CSRF
	CSRFTokenHeaderName = "X-CSRF-Token"
	// CSRFTokenFormField é o nome do campo de formulário para o token CSRF
	CSRFTokenFormField = "csrf_token"
	// CSRFTokenExpiration é o tempo de expiração do token em segundos
	CSRFTokenExpiration = 3600 // 1 hora
)

// TokenStore armazena os tokens CSRF válidos
type TokenStore struct {
	tokens map[string]time.Time
	mutex  sync.RWMutex
}

// NewTokenStore cria uma nova loja de tokens
func NewTokenStore() *TokenStore {
	return &TokenStore{
		tokens: make(map[string]time.Time),
	}
}

// Add adiciona um token à loja
func (ts *TokenStore) Add(token string) {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()
	ts.tokens[token] = time.Now().Add(time.Duration(CSRFTokenExpiration) * time.Second)
}

// Validate verifica se um token é válido e o remove se usado
func (ts *TokenStore) Validate(token string) bool {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	// Limpa tokens expirados periodicamente
	for t, exp := range ts.tokens {
		if time.Now().After(exp) {
			delete(ts.tokens, t)
		}
	}

	expiry, exists := ts.tokens[token]
	if !exists {
		return false
	}

	// Verifica se o token expirou
	if time.Now().After(expiry) {
		delete(ts.tokens, token)
		return false
	}

	// Token é válido, remove-o para prevenir reuso
	delete(ts.tokens, token)
	return true
}

// Instância global do armazenamento de tokens
var globalTokenStore = NewTokenStore()

// GenerateCSRFToken gera um novo token CSRF
func GenerateCSRFToken() (string, error) {
	b := make([]byte, CSRFTokenLength)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}

	token := base64.StdEncoding.EncodeToString(b)
	globalTokenStore.Add(token)
	return token, nil
}

// CSRFProtection é um middleware para proteção contra CSRF
func CSRFProtection() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Ignora métodos seguros (GET, HEAD, OPTIONS)
		if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
			// Apenas para GETs, gera um novo token e o define no cookie
			token, err := GenerateCSRFToken()
			if err != nil {
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "Falha ao gerar token CSRF"})
				return
			}

			// Define o token no cookie
			c.SetCookie(CSRFTokenCookieName, token, CSRFTokenExpiration, "/", "", false, false)

			// Define uma variável no contexto para que possa ser usado em templates
			c.Set("csrf_token", token)

			c.Next()
			return
		}

		// Para métodos não-seguros (POST, PUT, DELETE, etc), valida o token
		var tokenFromRequest string

		// Tenta obter o token do cabeçalho
		tokenFromRequest = c.GetHeader(CSRFTokenHeaderName)

		// Se não encontrado no cabeçalho, tenta obter do formulário
		if tokenFromRequest == "" {
			tokenFromRequest = c.PostForm(CSRFTokenFormField)
		}

		// Se ainda não encontrado, tenta obter do cookie como último recurso
		if tokenFromRequest == "" {
			tokenFromRequest, _ = c.Cookie(CSRFTokenCookieName)
		}

		if tokenFromRequest == "" {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Token CSRF ausente"})
			c.Abort()
			return
		}

		// Valida o token
		if !globalTokenStore.Validate(tokenFromRequest) {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "Token CSRF inválido ou expirado"})
			c.Abort()
			return
		}

		// Token validado, continua
		c.Next()
	}
}

// GetCSRFToken retorna o token CSRF atual ou gera um novo
func GetCSRFToken(c *gin.Context) (string, error) {
	// Tenta obter o token do contexto primeiro
	token, exists := c.Get("csrf_token")
	if exists {
		return token.(string), nil
	}

	// Tenta obter do cookie
	tokenStr, err := c.Cookie(CSRFTokenCookieName)
	if err == nil && tokenStr != "" {
		return tokenStr, nil
	}

	// Gera um novo token
	newToken, err := GenerateCSRFToken()
	if err != nil {
		return "", err
	}

	// Define o novo token no cookie
	c.SetCookie(CSRFTokenCookieName, newToken, CSRFTokenExpiration, "/", "", false, false)
	c.Set("csrf_token", newToken)

	return newToken, nil
}

// InjectCSRFToken injeta o token CSRF nos templates
func InjectCSRFToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := GetCSRFToken(c)
		if err != nil {
			fmt.Printf("Erro ao gerar token CSRF: %v\n", err)
			c.AbortWithError(http.StatusInternalServerError, errors.New("falha ao gerar token CSRF"))
			return
		}

		// Define o token no contexto para uso nos templates
		c.Set("csrf_token", token)
		c.Next()
	}
}
