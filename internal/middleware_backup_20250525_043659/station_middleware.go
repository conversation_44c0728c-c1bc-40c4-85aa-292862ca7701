package middleware

import (
	"log"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/gin-gonic/gin"
)

// StationMiddleware adiciona o ID da estação (filial) do usuário ao contexto
func StationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("[STATION-MIDDLEWARE] Iniciando middleware para rota: %s %s", c.Request.Method, c.Request.URL.Path)

		// Obtém o ID do usuário do contexto
		userIDInterface, exists := c.Get("userID")
		if !exists {
			log.Printf("[STATION-MIDDLEWARE] ID do usuário não encontrado no contexto")
			c.Next()
			return
		}
		log.Printf("[STATION-MIDDLEWARE] ID do usuário encontrado: %v (tipo: %T)", userIDInterface, userIDInterface)

		// Converte para uint
		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		default:
			log.Printf("[STATION-MIDDLEWARE] Tipo de userID não suportado: %T", userIDInterface)
			c.Next()
			return
		}

		// Obtém o perfil do usuário
		userRoleInterface, exists := c.Get("userRole")
		if !exists {
			log.Printf("[STATION-MIDDLEWARE] Perfil do usuário não encontrado no contexto")
			c.Next()
			return
		}

		userRole := userRoleInterface.(string)

		// Se o usuário não for do tipo filial, não precisa adicionar o StationID
		if userRole != string(models.RoleFilial) {
			log.Printf("[STATION-MIDDLEWARE] Usuário não é do tipo filial, não adicionando StationID")
			c.Next()
			return
		}

		// Obtém o StationID do usuário
		userRepo := repository.NewGormUserRepository()
		user, err := userRepo.FindByID(userID)
		if err != nil {
			log.Printf("[STATION-MIDDLEWARE] Erro ao obter usuário: %v", err)
			c.Next()
			return
		}

		// Verifica se o usuário tem um BranchID
		if user.BranchID == nil {
			log.Printf("[STATION-MIDDLEWARE] Usuário não tem BranchID")
			c.Next()
			return
		}

		// Adiciona o BranchID ao contexto como stationID
		c.Set("stationID", *user.BranchID)
		log.Printf("[STATION-MIDDLEWARE] StationID %d adicionado ao contexto para o usuário %d", *user.BranchID, userID)

		// Adiciona também o BranchID ao contexto para compatibilidade
		c.Set("branchID", *user.BranchID)
		log.Printf("[STATION-MIDDLEWARE] BranchID %d adicionado ao contexto para o usuário %d", *user.BranchID, userID)

		c.Next()
	}
}
