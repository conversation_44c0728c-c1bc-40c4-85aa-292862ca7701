package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// PasswordChangeMiddleware verifica se o usuário precisa trocar a senha
func PasswordChangeMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verificar se é uma rota de autenticação ou troca de senha
		path := c.Request.URL.Path
		if path == "/api/auth/login" || path == "/api/auth/change-password" ||
			path == "/change-password" || strings.HasPrefix(path, "/static/") ||
			strings.HasPrefix(path, "/uploads/") {
			c.Next()
			return
		}

		// Verificar se o usuário está autenticado
		_, exists := c.Get("userID")
		if !exists {
			c.Next()
			return
		}

		// Verificar se o usuário precisa trocar a senha
		forceChange, exists := c.Get("forcePasswordChange")
		if exists && forceChange.(bool) {
			// Se for uma requisição API
			if strings.HasPrefix(path, "/api/") {
				c.JSON(http.StatusForbidden, gin.H{
					"error":    "Troca de senha obrigatória",
					"redirect": "/change-password",
				})
				c.Abort()
				return
			}

			// Se for uma requisição de página
			c.Redirect(http.StatusFound, "/change-password")
			c.Abort()
			return
		}

		c.Next()
	}
}
