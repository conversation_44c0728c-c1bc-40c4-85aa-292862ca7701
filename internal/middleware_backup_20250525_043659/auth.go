package middleware

import (
	"crypto/rand"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// JWTSecret obtém a chave secreta do ambiente ou usa uma chave padrão em desenvolvimento
var JWTSecret []byte

// GlobalTokenBlacklist é a instância global da blacklist de tokens
var GlobalTokenBlacklist *TokenBlacklist

// InitTokenBlacklist inicializa a blacklist de tokens global
func InitTokenBlacklist() {
	// Inicializar JWTSecret apenas quando necessário
	if JWTSecret == nil {
		JWTSecret = []byte(getJWTSecret())
	}
	GlobalTokenBlacklist = NewTokenBlacklist()
	log.Printf("[AUTH] Blacklist de tokens inicializada")
}

// ensureJWTSecret garante que JWTSecret esteja inicializado
func ensureJWTSecret() {
	if JWTSecret == nil {
		JWTSecret = []byte(getJWTSecret())
	}
}

func getJWTSecret() string {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		if gin.Mode() == gin.ReleaseMode {
			log.Fatal("JWT_SECRET não configurado no ambiente de produção")
		}
		log.Println("AVISO: Usando chave JWT padrão em ambiente de desenvolvimento")
		return "chave-secreta-padrao-nao-use-em-producao"
	}
	return secret
}

// Claims representa as informações armazenadas no token JWT
type Claims struct {
	UserID uint   `json:"user_id"`
	Role   string `json:"role"`
	Email  string `json:"email"`
	jwt.RegisteredClaims
}

// AuthMiddleware verifica se o usuário está autenticado
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("[AUTH] Nova requisição para: %s %s", c.Request.Method, c.Request.URL.Path)
		log.Printf("[AUTH] Headers: %v", c.Request.Header)

		// Extrai o token da requisição
		tokenString, err := extractToken(c)
		if err != nil {
			log.Printf("[AUTH-ERROR] Erro ao extrair token: %v", err)
			log.Printf("[AUTH-DEBUG] Cookies presentes: %v", c.Request.Cookies())
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Token de autenticação não fornecido ou inválido"})
			c.Abort()
			return
		}

		log.Printf("[AUTH] Token extraído com sucesso (primeiros 10 caracteres): %s...", tokenString[:10])

		// Valida o token JWT
		claims, err := validateToken(tokenString)
		if err != nil {
			log.Printf("[AUTH-ERROR] Erro ao validar token: %v", err)
			// Diferenciar entre token expirado e outros erros
			if errors.Is(err, jwt.ErrTokenExpired) {
				log.Printf("[AUTH-ERROR] Token expirado")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Token expirado"})
			} else {
				log.Printf("[AUTH-ERROR] Token inválido: %v", err)
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Token inválido"})
			}
			c.Abort()
			return
		}

		log.Printf("[AUTH] Token validado com sucesso para usuário ID: %v, Role: %s", claims.UserID, claims.Role)

		// Adicionar informações do usuário ao contexto
		c.Set("userID", claims.UserID)
		c.Set("userRole", claims.Role)
		c.Set("email", claims.Email)

		log.Printf("[AUTH] Informações do usuário adicionadas ao contexto: ID=%v, Role=%s, Email=%s",
			claims.UserID, claims.Role, claims.Email)

		// Verifica se o token está próximo de expirar e precisa ser renovado
		if shouldRenewToken(claims) {
			log.Printf("[AUTH] Token próximo de expirar, renovando para usuário ID: %v", claims.UserID)

			// Gera um novo token
			newToken, err := renewToken(claims)
			if err != nil {
				log.Printf("[AUTH-ERROR] Erro ao renovar token: %v", err)
				// Continua com o token atual mesmo com erro na renovação
			} else {
				// Adiciona o token antigo à blacklist
				if GlobalTokenBlacklist != nil && claims.ExpiresAt != nil {
					expiry := claims.ExpiresAt.Time
					GlobalTokenBlacklist.AddToken(tokenString, expiry)
					log.Printf("[AUTH] Token antigo adicionado à blacklist, expira em: %v", expiry)
				}

				// Define o novo token no cookie
				c.SetCookie(
					"session_token",
					newToken,
					int(claims.ExpiresAt.Unix()-time.Now().Unix()), // Tempo restante em segundos
					"/",
					"",
					false, // Em produção, deveria ser true para HTTPS
					true,  // HttpOnly
				)

				// Define o cabeçalho para o cliente atualizar o token
				c.Header("X-Renew-Token", newToken)
				log.Printf("[AUTH] Token renovado com sucesso para usuário ID: %v", claims.UserID)
			}
		}

		c.Next()
	}
}

// RoleMiddleware verifica se o usuário tem a role necessária
func RoleMiddleware(roles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRoleStr, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Role do usuário não encontrada"})
			c.Abort()
			return
		}

		userRole := models.UserRole(userRoleStr.(string))
		hasRole := false
		for _, role := range roles {
			if userRole == role {
				hasRole = true
				break
			}
		}

		// Verificar também usando as funções de compatibilidade
		if !hasRole {
			for _, role := range roles {
				if models.IsTechnician(string(role)) && models.IsTechnician(string(userRole)) {
					hasRole = true
					break
				}
				if models.IsProvider(string(role)) && models.IsProvider(string(userRole)) {
					hasRole = true
					break
				}
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "Usuário não tem permissão para acessar este recurso"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// PageAccessMiddleware verifica se o usuário tem permissão para acessar uma página específica
// com base no sistema de permissões centralizado
func PageAccessMiddleware() gin.HandlerFunc {
	// Usar a implementação em permissoes.go
	return PageAccessMiddleware_Internal()
}

// extractToken extrai o token JWT do cabeçalho Authorization ou cookie
func extractToken(c *gin.Context) (string, error) {
	log.Printf("[AUTH] Tentando extrair token da requisição")

	// Tenta extrair do cabeçalho Authorization
	bearerToken := c.Request.Header.Get("Authorization")
	if bearerToken != "" {
		log.Printf("[AUTH] Token encontrado no cabeçalho Authorization")
		if strings.HasPrefix(bearerToken, "Bearer ") {
			return strings.TrimPrefix(bearerToken, "Bearer "), nil
		}
		return bearerToken, nil
	}

	// Tenta extrair do cookie
	cookie, err := c.Cookie("session_token")
	if err == nil && cookie != "" {
		log.Printf("[AUTH] Token encontrado no cookie session_token")
		return cookie, nil
	}

	log.Printf("[AUTH-ERROR] Nenhum token encontrado nos cabeçalhos ou cookies")
	return "", errors.New("token não encontrado")
}

// validateToken valida o token JWT
func validateToken(tokenString string) (*Claims, error) {
	log.Printf("[AUTH] Iniciando validação do token")

	// Garantir que JWTSecret esteja inicializado
	ensureJWTSecret()

	// Verificar se o token está na blacklist
	if GlobalTokenBlacklist != nil && GlobalTokenBlacklist.IsBlacklisted(tokenString) {
		log.Printf("[AUTH-ERROR] Token está na blacklist (invalidado por logout)")
		return nil, errors.New("token invalidado por logout")
	}

	// Primeiro, tenta extrair as claims sem validar a assinatura ou expiração
	// Isso é apenas para fins de log e não afeta a validação real
	var extractedClaims Claims
	_, _ = jwt.ParseWithClaims(tokenString, &extractedClaims, func(token *jwt.Token) (interface{}, error) {
		return JWTSecret, nil
	}, jwt.WithoutClaimsValidation())

	// Agora faz a validação completa
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Verifica se o método de assinatura é o esperado
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			log.Printf("[AUTH-ERROR] Método de assinatura inesperado: %v", token.Header["alg"])
			return nil, errors.New("método de assinatura inválido")
		}
		return JWTSecret, nil
	})

	if err != nil {
		// Se o token estiver expirado, registra o ID do usuário para fins de log
		if errors.Is(err, jwt.ErrTokenExpired) {
			log.Printf("[AUTH-ERROR] Token expirado para usuário ID: %v", extractedClaims.UserID)
		} else {
			log.Printf("[AUTH-ERROR] Erro ao fazer parse do token: %v", err)
		}
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		log.Printf("[AUTH] Token validado com sucesso para usuário ID: %v", claims.UserID)
		return claims, nil
	}

	log.Printf("[AUTH-ERROR] Token inválido")
	return nil, errors.New("token inválido")
}

// shouldRenewToken verifica se o token está prestes a expirar
func shouldRenewToken(claims *Claims) bool {
	// Define o limiar de renovação com base no perfil do usuário
	var threshold time.Time

	// Para perfis privilegiados (tokens de 24h), renova se faltar menos de 30 minutos
	if claims.Role == "admin" || claims.Role == "gerente" || claims.Role == "filial" {
		threshold = time.Now().Add(30 * time.Minute)
	} else {
		// Para outros perfis (tokens de 10min), renova se faltar menos de 2 minutos
		threshold = time.Now().Add(2 * time.Minute)
	}

	// Verifica se o token expira antes do limiar
	return claims.ExpiresAt != nil && claims.ExpiresAt.Before(threshold)
}

// renewToken cria um novo token com base no token atual
func renewToken(claims *Claims) (string, error) {
	// Garantir que JWTSecret esteja inicializado
	ensureJWTSecret()

	// Define novo tempo de expiração com base no perfil do usuário
	var expirationTime time.Time

	// Perfis privilegiados têm tokens com duração de 24 horas
	if claims.Role == "admin" || claims.Role == "gerente" || claims.Role == "filial" {
		expirationTime = time.Now().Add(24 * time.Hour)
		log.Printf("[AUTH] Renovando token de longa duração (24h) para usuário ID %d com perfil %s", claims.UserID, claims.Role)
	} else {
		// Outros perfis têm tokens com duração de 10 minutos
		expirationTime = time.Now().Add(10 * time.Minute)
		log.Printf("[AUTH] Renovando token de curta duração (10min) para usuário ID %d com perfil %s", claims.UserID, claims.Role)
	}

	// Gera um novo ID único para o token renovado
	tokenID, err := GenerateUniqueTokenID()
	if err != nil {
		log.Printf("[AUTH-ERROR] Erro ao gerar ID único para token renovado: %v", err)
		return "", err
	}

	// Cria novas claims com a mesma informação mas nova expiração
	newClaims := &Claims{
		UserID: claims.UserID,
		Role:   claims.Role,
		Email:  claims.Email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ID:        tokenID, // Novo ID único para o token renovado
		},
	}

	// Cria o token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, newClaims)

	// Assina o token
	return token.SignedString(JWTSecret)
}

// GenerateToken cria um novo token JWT para um usuário
func GenerateToken(userID uint, email, role string) (string, error) {
	// Garantir que JWTSecret esteja inicializado
	ensureJWTSecret()

	// Define tempo de expiração com base no perfil do usuário
	var expirationTime time.Time

	// Perfis privilegiados têm tokens com duração de 24 horas
	if role == "admin" || role == "gerente" || role == "filial" {
		expirationTime = time.Now().Add(24 * time.Hour)
		log.Printf("[AUTH] Gerando token de longa duração (24h) para usuário %s com perfil %s", email, role)
	} else {
		// Outros perfis têm tokens com duração de 10 minutos
		expirationTime = time.Now().Add(10 * time.Minute)
		log.Printf("[AUTH] Gerando token de curta duração (10min) para usuário %s com perfil %s", email, role)
	}

	// Gera um ID único para o token (JTI)
	tokenID, err := GenerateUniqueTokenID()
	if err != nil {
		log.Printf("[AUTH-ERROR] Erro ao gerar ID único para token: %v", err)
		return "", err
	}

	// Cria as claims
	claims := &Claims{
		UserID: userID,
		Role:   role,
		Email:  email,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			ID:        tokenID, // JWT ID (jti) - identificador único para este token
		},
	}

	// Cria o token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Assina o token
	return token.SignedString(JWTSecret)
}

// GenerateUniqueTokenID gera um identificador único para o token
// Exportada para uso em outros pacotes
func GenerateUniqueTokenID() (string, error) {
	// Gera 16 bytes aleatórios usando crypto/rand
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}

	// Converte para string hexadecimal no formato UUID
	return fmt.Sprintf("%x-%x-%x-%x-%x",
		b[0:4], b[4:6], b[6:8], b[8:10], b[10:]), nil
}

// UserService interface
type UserService interface {
	Authenticate(email, password string) (*User, error)
	GenerateToken(user *User) (string, error)
}

// User struct
type User struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
	Role  string `json:"role"`
}
