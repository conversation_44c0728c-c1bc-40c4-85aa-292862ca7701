package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// Este arquivo contém middlewares para redirecionar rotas antigas para novas,
// seguindo a convenção de nomenclatura padronizada do sistema.
// As rotas "/api/filiais" e "/api/stations" são redirecionadas para "/api/branches"
// com status 307 (Temporary Redirect) para manter compatibilidade com código existente.
// Para mais detalhes, consulte docs/guias/padronizacao_nomenclatura.md

// RedirectMiddleware cria um middleware para redirecionar rotas antigas para novas
func RedirectMiddleware(oldPrefix, newPrefix string, statusCode int) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// Verificar se o caminho começa com o prefixo antigo
		if strings.HasPrefix(path, oldPrefix) {
			// Construir o novo caminho
			newPath := strings.Replace(path, oldPrefix, newPrefix, 1)

			// Preservar query string
			if c.Request.URL.RawQuery != "" {
				newPath += "?" + c.Request.URL.RawQuery
			}

			// Redirecionar para o novo caminho
			c.Redirect(statusCode, newPath)
			c.Abort()
			return
		}

		// Continuar com a próxima função na cadeia
		c.Next()
	}
}

// FilialToBranchRedirectMiddleware redireciona rotas de /api/filiais para /api/branches
func FilialToBranchRedirectMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path

		// Verificar se o caminho começa com algum dos prefixos antigos
		var newPath string
		if strings.HasPrefix(path, "/api/filiais") {
			newPath = strings.Replace(path, "/api/filiais", "/api/branches", 1)
		} else if strings.HasPrefix(path, "/api/stations") {
			newPath = strings.Replace(path, "/api/stations", "/api/branches", 1)
		}

		// Se encontrou um prefixo para redirecionar
		if newPath != "" {
			// Preservar query string
			if c.Request.URL.RawQuery != "" {
				newPath += "?" + c.Request.URL.RawQuery
			}

			// Redirecionar para o novo caminho
			c.Redirect(http.StatusTemporaryRedirect, newPath)
			c.Abort()
			return
		}

		// Continuar com a próxima função na cadeia
		c.Next()
	}
}
