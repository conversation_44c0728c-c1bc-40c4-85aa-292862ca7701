package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
)

// CORS implementa o middleware de Cross-Origin Resource Sharing
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.<PERSON>.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		// Define o Content-Type apenas para rotas da API
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			// Verifica se o cliente está esperando JSON
			if strings.Contains(c.Request.Header.Get("Accept"), "application/json") {
				c.Writer.Header().Set("Content-Type", "application/json; charset=utf-8")
			}
		}

		c.Next()
	}
}
