package middleware

import (
	"log"
	"tradicao/internal/permissions"

	"github.com/gin-gonic/gin"
)

// VerificarPermissaoOrdem é um redirecionamento para o ResourcePermissionMiddleware
// Esta função existe apenas para compatibilidade com código existente
func VerificarPermissaoOrdem() gin.HandlerFunc {
	log.Println("AVISO: VerificarPermissaoOrdem está obsoleto. Use ResourcePermissionMiddleware em vez disso.")

	// Usar o middleware de permissão de recurso com o tipo de recurso "order" e a ação "view"
	return ResourcePermissionMiddleware(permissions.ResourceOrder, permissions.ActionView)
}
