package permissions

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// AuditLevel define o nível de detalhe da auditoria
type AuditLevel string

const (
	// AuditLevelNone desativa a auditoria
	AuditLevelNone AuditLevel = "none"
	// AuditLevelMinimal registra apenas tentativas de acesso negadas
	AuditLevelMinimal AuditLevel = "minimal"
	// AuditLevelBasic registra tentativas de acesso negadas e acessos a recursos sensíveis
	AuditLevelBasic AuditLevel = "basic"
	// AuditLevelFull registra todas as verificações de permissão
	AuditLevelFull AuditLevel = "full"
)

// AuditConfig define as configurações da auditoria de permissões
type AuditConfig struct {
	// Enabled indica se a auditoria está ativada
	Enabled bool `json:"enabled"`
	// Level define o nível de detalhe da auditoria
	Level AuditLevel `json:"level"`
	// LogSuccessfulChecks indica se verificações bem-sucedidas devem ser registradas
	LogSuccessfulChecks bool `json:"log_successful_checks"`
	// LogFailedChecks indica se verificações falhas devem ser registradas
	LogFailedChecks bool `json:"log_failed_checks"`
	// SensitiveResources define recursos considerados sensíveis
	SensitiveResources []string `json:"sensitive_resources"`
}

// DefaultAuditConfig retorna a configuração padrão da auditoria
func DefaultAuditConfig() AuditConfig {
	return AuditConfig{
		Enabled:             true,
		Level:               AuditLevelBasic,
		LogSuccessfulChecks: false,
		LogFailedChecks:     true,
		SensitiveResources: []string{
			"user",
			"security_policy",
			"audit_log",
			"maintenance_order",
		},
	}
}

// PermissionAuditService é o serviço de auditoria de permissões
type PermissionAuditService struct {
	db     *gorm.DB
	config AuditConfig
}

// NewPermissionAuditService cria um novo serviço de auditoria de permissões
func NewPermissionAuditService(db *gorm.DB) *PermissionAuditService {
	return &PermissionAuditService{
		db:     db,
		config: DefaultAuditConfig(),
	}
}

// SetConfig define a configuração da auditoria
func (s *PermissionAuditService) SetConfig(config AuditConfig) {
	s.config = config
}

// GetConfig retorna a configuração atual da auditoria
func (s *PermissionAuditService) GetConfig() AuditConfig {
	return s.config
}

// LogPermissionCheck registra uma verificação de permissão
func (s *PermissionAuditService) LogPermissionCheck(userID uint, userRole string, resourceType string, resourceID uint, action string, allowed bool, ip string, userAgent string) {
	// Verificar se a auditoria está ativada
	if !s.config.Enabled {
		return
	}

	// Verificar se deve registrar com base no resultado
	if allowed && !s.config.LogSuccessfulChecks {
		return
	}

	if !allowed && !s.config.LogFailedChecks {
		return
	}

	// Verificar o nível de auditoria
	if s.config.Level == AuditLevelNone {
		return
	}

	if s.config.Level == AuditLevelMinimal && allowed {
		return
	}

	if s.config.Level == AuditLevelBasic && allowed {
		// Verificar se é um recurso sensível
		isSensitive := false
		for _, r := range s.config.SensitiveResources {
			if r == resourceType {
				isSensitive = true
				break
			}
		}

		// Se não for sensível e o acesso foi permitido, não registrar
		if !isSensitive {
			return
		}
	}

	// Criar log de auditoria de permissão
	permissionLog := models.PermissionAuditLog{
		UserID:       &userID,
		UserRole:     userRole,
		ResourceType: resourceType,
		ResourceID:   resourceID,
		Action:       action,
		Allowed:      allowed,
		IPAddress:    ip,
		UserAgent:    userAgent,
		CreatedAt:    time.Now(),
	}

	// Salvar log de auditoria de permissão
	if err := s.db.Create(&permissionLog).Error; err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao salvar log de auditoria de permissão: %v", err)
	}

	// Para compatibilidade, também criar um log de auditoria geral
	details := map[string]interface{}{
		"resource_type": resourceType,
		"resource_id":   resourceID,
		"action":        action,
		"allowed":       allowed,
		"user_role":     userRole,
	}

	// Converter detalhes para JSON
	detailsJSON, err := json.Marshal(details)
	if err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao converter detalhes para JSON: %v", err)
		return
	}

	// Criar log de auditoria geral
	auditLog := models.AuditLog{
		UserID:    &userID,
		Action:    "permission_check",
		Resource:  fmt.Sprintf("%s:%d", resourceType, resourceID),
		Details:   string(detailsJSON),
		IP:        ip,
		UserAgent: userAgent,
		CreatedAt: time.Now(),
	}

	// Salvar log de auditoria geral
	if err := s.db.Create(&auditLog).Error; err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao salvar log de auditoria geral: %v", err)
	}
}

// LogPageAccess registra um acesso a uma página
func (s *PermissionAuditService) LogPageAccess(userID uint, userRole string, pagePath string, allowed bool, ip string, userAgent string) {
	// Verificar se a auditoria está ativada
	if !s.config.Enabled {
		return
	}

	// Verificar se deve registrar com base no resultado
	if allowed && !s.config.LogSuccessfulChecks {
		return
	}

	if !allowed && !s.config.LogFailedChecks {
		return
	}

	// Verificar o nível de auditoria
	if s.config.Level == AuditLevelNone {
		return
	}

	if s.config.Level == AuditLevelMinimal && allowed {
		return
	}

	// Criar detalhes do acesso
	details := map[string]interface{}{
		"page_path": pagePath,
		"allowed":   allowed,
		"user_role": userRole,
	}

	// Converter detalhes para JSON
	detailsJSON, err := json.Marshal(details)
	if err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao converter detalhes para JSON: %v", err)
		return
	}

	// Criar log de auditoria
	auditLog := models.AuditLog{
		UserID:    &userID,
		Action:    "page_access",
		Resource:  pagePath,
		Details:   string(detailsJSON),
		IP:        ip,
		UserAgent: userAgent,
		CreatedAt: time.Now(),
	}

	// Salvar log de auditoria
	if err := s.db.Create(&auditLog).Error; err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao salvar log de auditoria: %v", err)
	}
}

// LogAPIAccess registra um acesso a uma API
func (s *PermissionAuditService) LogAPIAccess(userID uint, userRole string, apiPath string, method string, allowed bool, ip string, userAgent string) {
	// Verificar se a auditoria está ativada
	if !s.config.Enabled {
		return
	}

	// Verificar se deve registrar com base no resultado
	if allowed && !s.config.LogSuccessfulChecks {
		return
	}

	if !allowed && !s.config.LogFailedChecks {
		return
	}

	// Verificar o nível de auditoria
	if s.config.Level == AuditLevelNone {
		return
	}

	if s.config.Level == AuditLevelMinimal && allowed {
		return
	}

	// Criar detalhes do acesso
	details := map[string]interface{}{
		"api_path":  apiPath,
		"method":    method,
		"allowed":   allowed,
		"user_role": userRole,
	}

	// Converter detalhes para JSON
	detailsJSON, err := json.Marshal(details)
	if err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao converter detalhes para JSON: %v", err)
		return
	}

	// Criar log de auditoria
	auditLog := models.AuditLog{
		UserID:    &userID,
		Action:    "api_access",
		Resource:  fmt.Sprintf("%s %s", method, apiPath),
		Details:   string(detailsJSON),
		IP:        ip,
		UserAgent: userAgent,
		CreatedAt: time.Now(),
	}

	// Salvar log de auditoria
	if err := s.db.Create(&auditLog).Error; err != nil {
		log.Printf("[PERMISSION-AUDIT] Erro ao salvar log de auditoria: %v", err)
	}
}

// GetAuditLogs retorna os logs de auditoria gerais
func (s *PermissionAuditService) GetAuditLogs(filters map[string]interface{}, page, pageSize int) ([]models.AuditLog, int64, error) {
	var logs []models.AuditLog
	var total int64

	// Construir query
	query := s.db.Model(&models.AuditLog{})

	// Aplicar filtros
	for key, value := range filters {
		query = query.Where(key, value)
	}

	// Contar total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Aplicar paginação
	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize)

	// Ordenar por data de criação (mais recentes primeiro)
	query = query.Order("created_at DESC")

	// Executar query
	if err := query.Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetPermissionAuditLogs retorna os logs de auditoria de permissões
func (s *PermissionAuditService) GetPermissionAuditLogs(userID *uint, userRole string, resourceType string, resourceID *uint, action string, allowed *bool, startDate, endDate *time.Time, page, pageSize int) ([]models.PermissionAuditLog, int64, error) {
	var logs []models.PermissionAuditLog
	var total int64

	// Construir query
	query := s.db.Model(&models.PermissionAuditLog{})

	// Aplicar filtros
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	if userRole != "" {
		query = query.Where("user_role = ?", userRole)
	}
	if resourceType != "" {
		query = query.Where("resource_type = ?", resourceType)
	}
	if resourceID != nil {
		query = query.Where("resource_id = ?", *resourceID)
	}
	if action != "" {
		query = query.Where("action = ?", action)
	}
	if allowed != nil {
		query = query.Where("allowed = ?", *allowed)
	}
	if startDate != nil {
		query = query.Where("created_at >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("created_at <= ?", *endDate)
	}

	// Contar total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Aplicar paginação
	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize)

	// Ordenar por data de criação (mais recentes primeiro)
	query = query.Order("created_at DESC")

	// Executar query
	if err := query.Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
