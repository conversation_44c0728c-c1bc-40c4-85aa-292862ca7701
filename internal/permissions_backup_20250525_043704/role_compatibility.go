package permissions

import (
	"log"

	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// RoleCompatibilityMiddleware é um middleware que normaliza os papéis dos usuários
// para garantir compatibilidade entre "tecnico" e "technician"
func RoleCompatibilityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém a role do usuário do contexto
		userRoleInterface, exists := c.Get("userRole")
		if !exists {
			// Se não existir, continua o fluxo
			c.Next()
			return
		}

		// Converte para string
		userRoleStr, ok := userRoleInterface.(string)
		if !ok {
			// Se não for string, continua o fluxo
			c.Next()
			return
		}

		// Normaliza o papel do usuário
		normalizedRole := models.NormalizeRole(userRoleStr)

		// Se o papel foi normalizado, atualiza no contexto
		if normalizedRole != userRoleStr {
			log.Printf("[ROLE-COMPATIBILITY] Normalizando papel do usuário: %s -> %s", userRoleStr, normalizedRole)
			c.Set("userRole", normalizedRole)
		}

		c.Next()
	}
}

// GetRoleFromConfig obtém a configuração de um papel do arquivo de permissões,
// considerando a compatibilidade entre "tecnico" e "technician"
func GetRoleFromConfig(config *Config, role string) (RoleConfig, bool) {
	// Verificar se o papel existe diretamente
	roleConfig, exists := config.Roles[role]
	if exists {
		return roleConfig, true
	}

	// Verificar compatibilidade para técnicos
	if models.IsTechnician(role) {
		// Tentar obter a configuração para "technician"
		roleConfig, exists = config.Roles["technician"]
		if exists {
			return roleConfig, true
		}

		// Tentar obter a configuração para "tecnico"
		roleConfig, exists = config.Roles["tecnico"]
		if exists {
			return roleConfig, true
		}
	}

	// Verificar compatibilidade para prestadores
	if models.IsProvider(role) {
		// Tentar obter a configuração para "provider"
		roleConfig, exists = config.Roles["provider"]
		if exists {
			return roleConfig, true
		}

		// Tentar obter a configuração para "prestador" ou "prestadores"
		roleConfig, exists = config.Roles["prestador"]
		if exists {
			return roleConfig, true
		}

		roleConfig, exists = config.Roles["prestadores"]
		if exists {
			return roleConfig, true
		}
	}

	// Não encontrou configuração compatível
	return RoleConfig{}, false
}
