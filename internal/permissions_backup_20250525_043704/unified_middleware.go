package permissions

import (
	"log"
	"net/http"
	"strconv"
	"strings"
	"tradicao/internal/models"

	"github.com/gin-gonic/gin"
)

// UnifiedMiddleware é o middleware unificado para verificação de permissões
type UnifiedMiddleware struct {
	service *UnifiedPermissionService
}

// NewUnifiedMiddleware cria um novo middleware unificado
func NewUnifiedMiddleware(service *UnifiedPermissionService) *UnifiedMiddleware {
	return &UnifiedMiddleware{
		service: service,
	}
}

// PageAccessMiddleware verifica se o usuário tem permissão para acessar uma página
func (m *UnifiedMiddleware) PageAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém a role do usuário do contexto
		userRole, exists := c.Get("userRole")
		if !exists {
			c.HTML(http.StatusUnauthorized, "auth/acesso_negado.html", gin.H{
				"title":   "Acesso Negado",
				"message": "<PERSON>u<PERSON>rio não autenticado",
			})
			c.Abort()
			return
		}

		// Obtém o caminho da página atual
		path := c.Request.URL.Path

		// Log detalhado para debug
		log.Printf("[AUTH-DEBUG] Verificando permissão para página: %s, perfil: %s", path, userRole.(string))

		// Obter ID do usuário
		userIDInterface, exists := c.Get("userID")
		if !exists {
			userIDInterface = uint(0)
		}

		// Converter para uint
		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		case string:
			// Não tentar converter, usar 0
			userID = 0
		default:
			userID = 0
		}

		// Obter IP e User-Agent
		ip := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// Verifica se o usuário tem permissão para acessar a página
		if !m.service.HasPagePermission(userID, userRole.(string), path, ip, userAgent) {
			userID, _ := c.Get("userID")
			email, _ := c.Get("email")

			// Obtém a lista de papéis com permissão para esta página
			permittedRoles := m.service.GetPermittedRoles(path, PagePermission)

			log.Printf("[AUTH-ERROR] ACESSO NEGADO: Usuário %v (email: %s, role: %s) tentou acessar página: %s (permissões requeridas: %v)",
				userID, email, userRole, path, permittedRoles)

			// Formata a lista de papéis permitidos como string para log
			var permittedRolesStr string
			if len(permittedRoles) > 0 {
				permittedRolesStr = strings.Join(permittedRoles, ", ")
			} else {
				permittedRolesStr = "Informação de permissão não disponível"
			}

			log.Printf("[AUTH-ERROR] Papéis permitidos: %s", permittedRolesStr)

			c.HTML(http.StatusForbidden, "auth/acesso_negado.html", gin.H{
				"title":   "Acesso Negado",
				"message": "Você não tem permissão para acessar esta página.",
				"user": gin.H{
					"ID":    userID,
					"Email": email,
					"Role":  userRole,
				},
				"path":           path,
				"permittedRoles": permittedRoles,
			})
			c.Abort()
			return
		}

		log.Printf("[AUTH-DEBUG] Acesso permitido para usuário com role '%s' à página: %s", userRole, path)
		c.Next()
	}
}

// APIAccessMiddleware verifica se o usuário tem permissão para acessar uma API
func (m *UnifiedMiddleware) APIAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém a role do usuário do contexto
		userRole, exists := c.Get("userRole")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Usuário não autenticado",
			})
			return
		}

		// Obtém o caminho da API atual
		path := c.Request.URL.Path
		method := c.Request.Method

		// Log detalhado para debug
		log.Printf("[AUTH-DEBUG] Verificando permissão para API: %s, método: %s, perfil: %s",
			path, method, userRole.(string))

		// Obter ID do usuário
		userIDInterface, exists := c.Get("userID")
		if !exists {
			userIDInterface = uint(0)
		}

		// Converter para uint
		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		case string:
			// Não tentar converter, usar 0
			userID = 0
		default:
			userID = 0
		}

		// Obter IP e User-Agent
		ip := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// Verifica se o usuário tem permissão para acessar a API
		if !m.service.HasAPIPermission(userID, userRole.(string), path, method, ip, userAgent) {
			userID, _ := c.Get("userID")
			email, _ := c.Get("email")

			// Obtém a lista de papéis com permissão para esta API
			permittedRoles := m.service.GetPermittedRoles(path, APIPermission)

			log.Printf("[AUTH-ERROR] ACESSO NEGADO: Usuário %v (email: %s, role: %s) tentou acessar API: %s (permissões requeridas: %v)",
				userID, email, userRole, path, permittedRoles)

			c.JSON(http.StatusForbidden, gin.H{
				"error": "Você não tem permissão para acessar este recurso",
			})
			c.Abort()
			return
		}

		log.Printf("[AUTH-DEBUG] Acesso permitido para usuário com role '%s' à API: %s", userRole, path)
		c.Next()
	}
}

// ResourcePermissionMiddleware verifica se o usuário tem permissão para acessar um recurso específico
func (m *UnifiedMiddleware) ResourcePermissionMiddleware(resourceType ResourceType, action Action) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Obtém a role do usuário do contexto
		userRole, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "Usuário não autenticado",
			})
			c.Abort()
			return
		}

		// Obtém o ID do usuário do contexto
		userIDInterface, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "ID do usuário não encontrado",
			})
			c.Abort()
			return
		}

		// Converter userID para uint
		var userID uint
		switch v := userIDInterface.(type) {
		case uint:
			userID = v
		case int:
			userID = uint(v)
		case int64:
			userID = uint(v)
		case float64:
			userID = uint(v)
		case string:
			// Converter string para uint
			userIDInt, err := strconv.ParseUint(v, 10, 32)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "Erro ao converter ID do usuário",
				})
				c.Abort()
				return
			}
			userID = uint(userIDInt)
		default:
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Tipo de ID do usuário não suportado",
			})
			c.Abort()
			return
		}

		// Obtém o ID do recurso do parâmetro da URL
		resourceIDStr := c.Param("id")
		if resourceIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do recurso não fornecido",
			})
			c.Abort()
			return
		}

		// Converter para int
		resourceID, err := strconv.Atoi(resourceIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "ID do recurso inválido",
			})
			c.Abort()
			return
		}

		// Obter IP e User-Agent
		ip := c.ClientIP()
		userAgent := c.Request.UserAgent()

		// Verificar permissão
		if !m.service.HasResourcePermission(userID, userRole.(string), resourceType, uint(resourceID), action, ip, userAgent) {
			log.Printf("[RESOURCE-MIDDLEWARE] Acesso negado: Usuário %d (role: %s) tentou %s recurso %s:%d",
				userID, userRole, action, resourceType, resourceID)
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": "Você não tem permissão para acessar este recurso",
			})
			c.Abort()
			return
		}

		log.Printf("[RESOURCE-MIDDLEWARE] Acesso permitido: Usuário %d (role: %s) para %s recurso %s:%d",
			userID, userRole, action, resourceType, resourceID)
		c.Next()
	}
}

// RoleMiddleware verifica se o usuário tem um dos papéis especificados
func (m *UnifiedMiddleware) RoleMiddleware(roles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRoleInterface, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Role do usuário não encontrada"})
			c.Abort()
			return
		}

		userRoleStr := userRoleInterface.(string)
		userRole := models.UserRole(userRoleStr)

		hasRole := false
		for _, role := range roles {
			if userRole == role {
				hasRole = true
				break
			}
		}

		// Verificar também usando as funções de compatibilidade
		if !hasRole {
			for _, role := range roles {
				if models.IsTechnician(string(role)) && models.IsTechnician(userRoleStr) {
					hasRole = true
					break
				}
				if models.IsProvider(string(role)) && models.IsProvider(userRoleStr) {
					hasRole = true
					break
				}
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "Usuário não tem permissão para acessar este recurso"})
			c.Abort()
			return
		}

		c.Next()
	}
}
