package permissions

// Permission representa uma permissão individual
type Permission string

// Role representa um papel de usuário
type Role string

// PermissionType representa o tipo de permissão (página, API ou recurso)
type PermissionType string

const (
	// PagePermission representa permissão para acessar uma página
	PagePermission PermissionType = "page"

	// APIPermission representa permissão para acessar uma API
	APIPermission PermissionType = "api"

	// ResourcePermission representa permissão para acessar um recurso específico
	ResourcePermission PermissionType = "resource"
)

// Action representa uma ação que pode ser realizada em um recurso
type Action string

const (
	// ActionView representa a ação de visualizar um recurso
	ActionView Action = "view"

	// ActionCreate representa a ação de criar um recurso
	ActionCreate Action = "create"

	// ActionUpdate representa a ação de atualizar um recurso
	ActionUpdate Action = "update"

	// ActionDelete representa a ação de excluir um recurso
	ActionDelete Action = "delete"

	// ActionAssign representa a ação de atribuir um recurso
	ActionAssign Action = "assign"
)

// ResourceType representa o tipo de recurso
type ResourceType string

const (
	// ResourceOrder representa um recurso do tipo ordem de manutenção
	ResourceOrder ResourceType = "order"

	// ResourceEquipment representa um recurso do tipo equipamento
	ResourceEquipment ResourceType = "equipment"

	// ResourceBranch representa um recurso do tipo filial
	ResourceBranch ResourceType = "branch"

	// ResourceTechnician representa um recurso do tipo técnico
	ResourceTechnician ResourceType = "technician"

	// ResourceServiceProvider representa um recurso do tipo prestador de serviço
	ResourceServiceProvider ResourceType = "service_provider"
)

// RoleConfig representa a configuração de um papel
type RoleConfig struct {
	Description string              `yaml:"description"`
	Pages       []string            `yaml:"pages"`
	APIs        []string            `yaml:"apis"`
	Resources   map[string][]string `yaml:"resources,omitempty"` // Mapa de recurso -> ações permitidas
}

// Config representa a configuração completa de permissões
type Config struct {
	Roles       map[string]RoleConfig `yaml:"roles"`
	PublicPages []string              `yaml:"public_pages"`
	PublicAPIs  []string              `yaml:"public_apis"`
}
