package permissions

import (
	"fmt"
	"io/ioutil"
	"log"
	"strings"
	"sync"
	"time"

	"gopkg.in/yaml.v3"
)

// PermissionCacheKey é a chave para o cache de permissões
type PermissionCacheKey struct {
	UserID       uint
	ResourceType string
	ResourceID   uint
	Action       string
}

// PermissionCacheEntry é uma entrada no cache de permissões
type PermissionCacheEntry struct {
	Allowed   bool
	Timestamp time.Time
}

// Service é o serviço de permissões
type Service struct {
	mutex          sync.RWMutex
	config         *Config
	configPath     string
	cache          map[PermissionCacheKey]PermissionCacheEntry
	cacheMutex     sync.RWMutex
	cacheTTL       time.Duration
	cacheEnabled   bool
	cacheHits      int
	cacheMisses    int
	cacheEvictions int
}

// NewService cria uma nova instância do serviço de permissões
func NewService(configPath string) (*Service, error) {
	service := &Service{
		configPath:     configPath,
		cache:          make(map[PermissionCacheKey]PermissionCacheEntry),
		cacheTTL:       5 * time.Minute, // Cache válido por 5 minutos
		cacheEnabled:   true,            // Cache habilitado por padrão
		cacheHits:      0,
		cacheMisses:    0,
		cacheEvictions: 0,
	}

	if err := service.ReloadConfig(configPath); err != nil {
		return nil, err
	}

	// Iniciar rotina de limpeza do cache
	go service.startCacheCleanup()

	return service, nil
}

// startCacheCleanup inicia uma rotina de limpeza do cache
func (s *Service) startCacheCleanup() {
	ticker := time.NewTicker(s.cacheTTL)
	defer ticker.Stop()

	for range ticker.C {
		s.cleanupCache()
	}
}

// cleanupCache remove entradas expiradas do cache
func (s *Service) cleanupCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	now := time.Now()
	expiredKeys := make([]PermissionCacheKey, 0)

	// Identificar entradas expiradas
	for key, entry := range s.cache {
		if now.Sub(entry.Timestamp) > s.cacheTTL {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// Remover entradas expiradas
	for _, key := range expiredKeys {
		delete(s.cache, key)
		s.cacheEvictions++
	}

	if len(expiredKeys) > 0 {
		log.Printf("Cache de permissões: %d entradas expiradas removidas", len(expiredKeys))
	}
}

// HasPermission verifica se um papel tem permissão para acessar um recurso
func (s *Service) HasPermission(role string, resourcePath string, permType PermissionType) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Remover a barra inicial, se houver
	resourcePath = strings.TrimPrefix(resourcePath, "/")

	// Verificar se é um recurso público
	if permType == PagePermission {
		for _, publicPage := range s.config.PublicPages {
			if publicPage == resourcePath {
				return true
			}
		}
	} else if permType == APIPermission {
		for _, publicAPI := range s.config.PublicAPIs {
			if publicAPI == resourcePath {
				return true
			}
		}
	}

	// Administrador tem acesso a tudo
	if role == "admin" {
		return true
	}

	// Verificar se o papel existe, considerando compatibilidade
	roleConfig, exists := GetRoleFromConfig(s.config, role)
	if !exists {
		log.Printf("AVISO: Papel não encontrado: %s", role)
		return false
	}

	// Verificar permissões específicas
	var permissions []string
	if permType == PagePermission {
		permissions = roleConfig.Pages
	} else {
		permissions = roleConfig.APIs
	}

	// Verificar permissão direta
	for _, perm := range permissions {
		// Permissão curinga
		if perm == "*" {
			return true
		}

		// Permissão exata
		if perm == resourcePath {
			return true
		}

		// Permissão com parâmetros (ex: "maintenance/view/:id")
		if strings.Contains(perm, ":") {
			pattern := strings.Split(perm, ":")
			if strings.HasPrefix(resourcePath, pattern[0]) {
				return true
			}
		}

		// Verificar prefixo para URLs com parâmetros (ex: "maintenance/view/123")
		if strings.HasPrefix(resourcePath, perm+"/") {
			return true
		}
	}

	return false
}

// GetConfig retorna a configuração atual
func (s *Service) GetConfig() *Config {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.config
}

// ReloadConfig recarrega a configuração do arquivo
func (s *Service) ReloadConfig(configPath string) error {
	config, err := LoadConfig(configPath)
	if err != nil {
		return err
	}

	s.mutex.Lock()
	s.config = config
	s.mutex.Unlock()

	return nil
}

// UpdateRolePermissions atualiza as permissões de um papel
func (s *Service) UpdateRolePermissions(role string, pages []string, apis []string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if _, exists := s.config.Roles[role]; !exists {
		return fmt.Errorf("papel não encontrado: %s", role)
	}

	if role == "admin" {
		return fmt.Errorf("não é permitido modificar as permissões do administrador")
	}

	// Validar páginas e APIs antes de atualizar
	if err := s.validatePermissions(pages, apis); err != nil {
		return err
	}

	// Atualizar configuração
	s.config.Roles[role] = RoleConfig{
		Description: s.config.Roles[role].Description, // Mantém a descrição original
		Pages:       pages,
		APIs:        apis,
	}

	return s.saveConfig()
}

// validatePermissions valida as permissões antes de atualizar
func (s *Service) validatePermissions(pages []string, apis []string) error {
	// Validar páginas
	for _, page := range pages {
		if !s.isValidPage(page) {
			return fmt.Errorf("página inválida: %s", page)
		}
	}

	// Validar APIs
	for _, api := range apis {
		if !s.isValidAPI(api) {
			return fmt.Errorf("API inválida: %s", api)
		}
	}

	return nil
}

// isValidPage verifica se uma página é válida
func (s *Service) isValidPage(page string) bool {
	// Implementar lógica de validação de páginas
	// Por exemplo, verificar contra uma lista de páginas válidas
	return true
}

// isValidAPI verifica se uma API é válida
func (s *Service) isValidAPI(api string) bool {
	// Implementar lógica de validação de APIs
	// Por exemplo, verificar contra uma lista de APIs válidas
	return true
}

// saveConfig salva a configuração atual no arquivo
func (s *Service) saveConfig() error {
	data, err := yaml.Marshal(s.config)
	if err != nil {
		return fmt.Errorf("erro ao converter para YAML: %v", err)
	}

	header := "# Sistema de Permissões Centralizado\n# Rede Tradição - Sistema de Manutenção\n\n"
	data = []byte(header + string(data))

	if err := ioutil.WriteFile(s.configPath, data, 0644); err != nil {
		return fmt.Errorf("erro ao salvar arquivo: %v", err)
	}

	return nil
}

// HasResourcePermission verifica se um usuário tem permissão para acessar um recurso específico
func (s *Service) HasResourcePermission(userID uint, resourceType ResourceType, resourceID uint, action Action) bool {
	// Verificar cache primeiro, se estiver habilitado
	if s.cacheEnabled {
		cacheKey := PermissionCacheKey{
			UserID:       userID,
			ResourceType: string(resourceType),
			ResourceID:   resourceID,
			Action:       string(action),
		}

		// Verificar se existe no cache
		s.cacheMutex.RLock()
		entry, exists := s.cache[cacheKey]
		s.cacheMutex.RUnlock()

		if exists && time.Since(entry.Timestamp) < s.cacheTTL {
			// Cache hit
			s.cacheMutex.Lock()
			s.cacheHits++
			s.cacheMutex.Unlock()
			return entry.Allowed
		}

		// Cache miss
		s.cacheMutex.Lock()
		s.cacheMisses++
		s.cacheMutex.Unlock()
	}

	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Administrador tem acesso a tudo
	// Aqui precisaríamos verificar se o usuário é administrador
	// Como não temos acesso direto ao banco de dados aqui, vamos assumir que isso seria verificado em outro lugar
	// e passado como parâmetro adicional

	// Verificar permissões específicas para o tipo de recurso
	var allowed bool

	// Aqui implementaríamos a lógica específica para cada tipo de recurso
	// Por exemplo, para ordens de manutenção:
	if resourceType == ResourceOrder {
		// Implementar lógica específica para ordens
		// Por exemplo, verificar se o usuário é o criador da ordem, ou se é um técnico atribuído, etc.
		allowed = s.hasOrderPermission(userID, resourceID, action)
	} else if resourceType == ResourceEquipment {
		// Implementar lógica específica para equipamentos
		allowed = s.hasEquipmentPermission(userID, resourceID, action)
	} else if resourceType == ResourceBranch {
		// Implementar lógica específica para filiais
		allowed = s.hasBranchPermission(userID, resourceID, action)
	} else if resourceType == ResourceTechnician {
		// Implementar lógica específica para técnicos
		allowed = s.hasTechnicianPermission(userID, resourceID, action)
	} else if resourceType == ResourceServiceProvider {
		// Implementar lógica específica para prestadores de serviço
		allowed = s.hasServiceProviderPermission(userID, resourceID, action)
	} else {
		// Se não for um tipo de recurso conhecido, negar acesso
		allowed = false
	}

	// Atualizar cache, se estiver habilitado
	if s.cacheEnabled {
		cacheKey := PermissionCacheKey{
			UserID:       userID,
			ResourceType: string(resourceType),
			ResourceID:   resourceID,
			Action:       string(action),
		}

		s.cacheMutex.Lock()
		s.cache[cacheKey] = PermissionCacheEntry{
			Allowed:   allowed,
			Timestamp: time.Now(),
		}
		s.cacheMutex.Unlock()
	}

	return allowed
}

// Nota: As implementações destes métodos foram movidas para o arquivo resource_permissions.go
// para evitar duplicação de código.

// EnableCache habilita o cache de permissões
func (s *Service) EnableCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	s.cacheEnabled = true
	log.Println("Cache de permissões habilitado")
}

// DisableCache desabilita o cache de permissões
func (s *Service) DisableCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	s.cacheEnabled = false
	log.Println("Cache de permissões desabilitado")
}

// ClearCache limpa o cache de permissões
func (s *Service) ClearCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	s.cache = make(map[PermissionCacheKey]PermissionCacheEntry)
	log.Println("Cache de permissões limpo")
}

// GetCacheStats retorna estatísticas do cache
func (s *Service) GetCacheStats() map[string]int {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()

	return map[string]int{
		"hits":      s.cacheHits,
		"misses":    s.cacheMisses,
		"evictions": s.cacheEvictions,
		"size":      len(s.cache),
	}
}

// SetCacheTTL define o tempo de vida do cache
func (s *Service) SetCacheTTL(ttl time.Duration) {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	s.cacheTTL = ttl
	log.Printf("TTL do cache de permissões definido para %v", ttl)
}

// GetPermittedRoles retorna a lista de papéis que têm permissão para acessar um recurso
func (s *Service) GetPermittedRoles(resourcePath string, permType PermissionType) []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Remover a barra inicial, se houver
	resourcePath = strings.TrimPrefix(resourcePath, "/")

	// Lista para armazenar os papéis permitidos
	permittedRoles := make([]string, 0)

	// Verificar se é um recurso público
	if permType == PagePermission {
		for _, publicPage := range s.config.PublicPages {
			if publicPage == resourcePath {
				// Se for público, todos têm acesso
				return []string{"*"}
			}
		}
	} else if permType == APIPermission {
		for _, publicAPI := range s.config.PublicAPIs {
			if publicAPI == resourcePath {
				// Se for público, todos têm acesso
				return []string{"*"}
			}
		}
	}

	// Admin sempre tem acesso
	permittedRoles = append(permittedRoles, "admin")

	// Verificar cada papel
	for role, roleConfig := range s.config.Roles {
		if role == "admin" {
			continue // Admin já foi adicionado
		}

		// Selecionar lista de permissões baseado no tipo
		var permissions []string
		if permType == PagePermission {
			permissions = roleConfig.Pages
		} else {
			permissions = roleConfig.APIs
		}

		// Verificar permissões
		for _, perm := range permissions {
			// Permissão curinga
			if perm == "*" {
				permittedRoles = append(permittedRoles, role)
				break
			}

			// Permissão exata
			if perm == resourcePath {
				permittedRoles = append(permittedRoles, role)
				break
			}

			// Permissão com parâmetros (ex: "maintenance/view/:id")
			if strings.Contains(perm, ":") {
				pattern := strings.Split(perm, ":")
				if strings.HasPrefix(resourcePath, pattern[0]) {
					permittedRoles = append(permittedRoles, role)
					break
				}
			}

			// Verificar prefixo para URLs com parâmetros
			if strings.HasPrefix(resourcePath, perm+"/") {
				permittedRoles = append(permittedRoles, role)
				break
			}
		}
	}

	return permittedRoles
}
