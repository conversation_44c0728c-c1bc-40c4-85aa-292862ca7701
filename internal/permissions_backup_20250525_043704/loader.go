package permissions

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"

	"gopkg.in/yaml.v3"
)

// LoadConfig carrega a configuração de permissões do arquivo YAML
func LoadConfig(filePath string) (*Config, error) {
	log.Printf("Carregando configuração de permissões de: %s", filePath)

	// Verificar se o arquivo existe
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("arquivo de configuração não encontrado: %s", filePath)
	}

	// Ler o arquivo
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("erro ao ler arquivo de configuração: %v", err)
	}

	// Decodificar o YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("erro ao decodificar YAML: %v", err)
	}

	// Validar a configuração
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("configuração inválida: %v", err)
	}

	log.Printf("Configuração de permissões carregada com sucesso: %d papéis definidos", len(config.Roles))
	return &config, nil
}

// validateConfig valida a configuração de permissões
func validateConfig(config *Config) error {
	if config.Roles == nil {
		return fmt.Errorf("nenhum papel definido na configuração")
	}

	// Verificar se há pelo menos um papel definido
	if len(config.Roles) == 0 {
		return fmt.Errorf("nenhum papel definido na configuração")
	}

	// Verificar se o papel de admin existe
	if _, exists := config.Roles["admin"]; !exists {
		log.Printf("AVISO: Papel 'admin' não definido na configuração")
	}

	return nil
}

// DumpConfig imprime a configuração de permissões para debug
func DumpConfig(config *Config) {
	log.Println("============= CONFIGURAÇÃO DE PERMISSÕES =============")

	log.Println("Papéis definidos:")
	for role, roleConfig := range config.Roles {
		log.Printf("  %s: %s", role, roleConfig.Description)
		log.Printf("    Páginas: %v", roleConfig.Pages)
		log.Printf("    APIs: %v", roleConfig.APIs)
	}

	log.Println("Páginas públicas:")
	for _, page := range config.PublicPages {
		log.Printf("  %s", page)
	}

	log.Println("APIs públicas:")
	for _, api := range config.PublicAPIs {
		log.Printf("  %s", api)
	}

	log.Println("=====================================================")
}
