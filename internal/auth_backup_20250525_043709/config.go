package auth

import (
	"os"
	"time"
)

type JWTConfig struct {
	SecretKey         string
	Expiration        time.Duration
	RefreshExpiration time.Duration
}

func NewJWTConfig() *JWTConfig {
	secretKey := os.Getenv("JWT_SECRET_KEY")
	if secretKey == "" {
		secretKey = "sua_chave_secreta_padrao" // Em produção, sempre use uma chave segura
	}

	return &JWTConfig{
		SecretKey:         secretKey,
		Expiration:        24 * time.Hour,     // Token expira em 24 horas
		RefreshExpiration: 7 * 24 * time.Hour, // Refresh token expira em 7 dias
	}
}
