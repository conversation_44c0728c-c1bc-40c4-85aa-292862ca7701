package auth

import "github.com/gin-gonic/gin"

// Auth é a interface principal do pacote de autenticação
type Auth interface {
	HashPassword(password string) (string, error)
	CheckPassword(password, hash string) bool
	GenerateToken(userID int, username, role string) (string, error)
	ValidateToken(tokenString string) (*Claims, error)
	AuthMiddleware() gin.HandlerFunc
	RoleMiddleware(requiredRole string) gin.HandlerFunc
	LoginHandler(c *gin.Context)
	LogoutHandler(c *gin.Context)
	ProfileHandler(c *gin.Context)
	RegisterRoutes(r *gin.RouterGroup)
}

// New cria uma nova instância do serviço de autenticação
func New() Auth {
	config := NewJWTConfig()
	return NewAuthService(config)
}
